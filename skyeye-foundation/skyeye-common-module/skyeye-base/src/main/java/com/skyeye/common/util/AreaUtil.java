/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.skyeye.common.constans.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AreaUtil
 * @Description: 初始化地区部
 * @author: skyeye云系列--卫志强
 * @date: 2021/7/6 22:03
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public class AreaUtil {

    /**
     * 获取地区api
     */
    private static final String URL_JD_AREA = "https://fts.jd.com/area/get?fid=%d";

    /**
     * 初始化省份数据
     */
    private static final String[] TABLE_PROVINCE = new String[]{"1", "北京", "2", "上海", "3", "天津", "4", "重庆", "5", "河北",
        "6", "山西", "7", "河南", "8", "辽宁", "9", "吉林", "10", "黑龙江", "11", "内蒙古", "12", "江苏", "13", "山东", "14", "安徽",
        "15", "浙江", "16", "福建", "17", "湖北", "18", "湖南", "19", "广东", "20", "广西", "21", "江西", "22", "四川", "23", "海南",
        "24", "贵州", "25", "云南", "26", "西藏", "27", "陕西", "28", "甘肃", "29", "青海", "30", "宁夏", "31", "新疆", "32", "台湾",
        "42", "香港", "43", "澳门", "84", "钓鱼岛"};

    private static final String USER_ID = CommonConstants.ADMIN_USER_ID;

    private static final String TIME = DateUtil.getTimeAndToString();

    /**
     * 初始化省份数据
     */
    public static void initArea() {
        try {
            Connection conn = getConn("172.18.92.40", "3308", "eve", "root", "mysql@ruiyi123?");
            Map<Integer, String> dataMap = getProvinceMap(conn);
            for (int nIndex = 0; nIndex < TABLE_PROVINCE.length; nIndex = nIndex + 2) {
                int id = Integer.parseInt(TABLE_PROVINCE[nIndex]);
                String name = TABLE_PROVINCE[nIndex + 1];
                if (StrUtil.isNotEmpty(dataMap.get(id))) {
                    updateArea(conn, dataMap.get(id), id, name, 0, 0);
                } else {
                    insertArea(conn, ToolUtil.getSurFaceId(), id, name, 0, 0);
                }
                initChildArea(conn, id, 1, dataMap);
            }
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取各省下级地区
     *
     * @param conn     数据库连接对象
     * @param parentId 所属地区ID
     * @param level    地区层级，省级：0，市级：1，...
     */
    public static void initChildArea(Connection conn, int parentId, int level, Map<Integer, String> dataMap) {
        String url = String.format(URL_JD_AREA, parentId);
        String text = HttpClient.doGet(url);
        if (!StringUtils.isEmpty(text)) {
            List<Map<String, Object>> array = JSONUtil.toList(text, null);
            if (array != null && array.size() > 0) {
                for (int nIndex = 0; nIndex < array.size(); nIndex++) {
                    Map<String, Object> object = array.get(nIndex);
                    int id = Integer.parseInt(object.get("id").toString());
                    String name = object.get("name").toString();
                    if (StrUtil.isNotEmpty(dataMap.get(id))) {
                        updateArea(conn, dataMap.get(id), id, name, parentId, level);
                    } else {
                        insertArea(conn, ToolUtil.getSurFaceId(), id, name, parentId, level);
                    }
                    initChildArea(conn, id, level + 1, dataMap);
                }
            }
        }
    }

    public static void insertArea(Connection conn, String dataId, int codeId, String name, int parentId, int level) {
        try {
            Statement stat = conn.createStatement();
            String sql = "INSERT INTO t_area VALUES ('" + dataId + "', " + codeId + ", '" + name + "', " + parentId + ", " + level
                + ", '" + USER_ID + "', '" + TIME + "', '" + USER_ID + "', '" + TIME + "')";
            stat.execute(sql);
            stat.close();
            System.out.println("新增：" + name + "--级别：" + level);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static void updateArea(Connection conn, String dataId, int codeId, String name, int parentId, int level) {
        try {
            Statement stat = conn.createStatement();
            String sql = "UPDATE t_area SET code_id = " + codeId + ", name = '" + name + "', parent_code_id = " + parentId + ", level = " + level
                + ", last_update_id = '" + USER_ID + "', last_update_time = '" + TIME + "' WHERE id = '" + dataId + "'";
            stat.execute(sql);
            stat.close();
            System.out.println("更新：" + name + "--级别：" + level);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static Map<Integer, String> getProvinceMap(Connection conn) {
        try {
            Statement stat = conn.createStatement();
            String sql = "SELECT id, code_id FROM t_area";
            ResultSet resultSet = stat.executeQuery(sql);
            Map<Integer, String> map = new java.util.HashMap<>();
            while (resultSet.next()) {
                map.put(resultSet.getInt("code_id"), resultSet.getString("id"));
            }
            return map;
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 链接数据库
     *
     * @param dbHost     数据库主机地址
     * @param dbPort     数据库端口
     * @param dbName     数据库名称
     * @param dbUser     数据库用户名称
     * @param dbPassword 数据库登录密码
     * @return
     * @throws Exception
     */
    public static Connection getConn(String dbHost, String dbPort, String dbName, String dbUser, String dbPassword) throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        Class.forName("com.mysql.jdbc.Driver").newInstance();
        String connStr = "jdbc:mysql://" + dbHost + ":" + dbPort + "/" + dbName + "?user=" + dbUser + "&password=" + dbPassword + "&useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&rewriteBatchedStatements=true&serverTimezone=UTC";
        Connection conn = DriverManager.getConnection(connStr);
        return conn;
    }

    public static void main(String[] args) {
        initArea();
    }

}
